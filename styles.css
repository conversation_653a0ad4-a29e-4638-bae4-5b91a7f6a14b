/* CSS变量定义 */
:root {
    --primary-blue: #2A6AFF;
    --secondary-blue: #5C96FF;
    --light-blue: #E9EEFD;
    --dark-blue: #081C4D;
    --text-dark: #333333;
    --text-gray: #666666;
    --text-light: #999999;
    --white: #FFFFFF;
    --border-gray: #E4E4E4;
    --shadow: 0px 10px 10px 0px rgba(0, 0, 0, 0.08);
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Source Han Sans CN', 'Microsoft YaHei', sans-serif;
    line-height: 1.5;
    color: var(--text-dark);
    background-color: var(--white);
}

/* 顶部导航栏 */
.header {
    background: var(--white);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav {
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 60px;
}

.logo-img {
    height: 40px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
}

.nav-item {
    font-size: 16px;
    color: var(--text-dark);
    cursor: pointer;
    transition: color 0.3s;
}

.nav-item.active {
    color: var(--primary-blue);
    font-weight: 500;
}

.nav-item:hover {
    color: var(--primary-blue);
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.console-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-dark);
    cursor: pointer;
}

.login-btn {
    background: var(--primary-blue);
    color: var(--white);
    border: none;
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s;
}

.login-btn:hover {
    background: var(--secondary-blue);
}

/* Hero Banner区域 */
.hero-section {
    background: linear-gradient(135deg, var(--light-blue) 0%, #BDE4FD 100%);
    padding: 80px 40px;
    min-height: 600px;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 1920px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.hero-title {
    font-size: 52px;
    font-weight: 400;
    line-height: 1.3;
    margin-bottom: 40px;
}

.hero-subtitle {
    background: linear-gradient(270deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 64px;
}

.cta-button {
    background: linear-gradient(270deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    border: none;
    padding: 15px 30px;
    border-radius: 6px;
    font-size: 18px;
    cursor: pointer;
    transition: transform 0.3s;
}

.cta-button:hover {
    transform: translateY(-2px);
}

.product-showcase {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.product-item {
    text-align: center;
}

.product-bg {
    position: relative;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-bg.blue-bg {
    background: #BDE4FD;
}

.product-bg.green-bg {
    background: #D2E6EB;
}

.product-bg.purple-bg {
    background: #CDD1FF;
}

.product-bg img {
    max-width: 80%;
    max-height: 80%;
    object-fit: contain;
}

.product-label {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: linear-gradient(270deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.product-item p {
    font-size: 16px;
    color: var(--text-dark);
    font-weight: 500;
}

/* 平台特色区域 */
.features-section {
    padding: 80px 40px;
    background: var(--white);
}

.section-title {
    text-align: center;
    font-size: 36px;
    font-weight: 500;
    margin-bottom: 60px;
    color: var(--text-dark);
}

.features-grid {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
}

.feature-item {
    text-align: center;
    padding: 40px 20px;
}

.feature-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.feature-item h3 {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-dark);
}

/* 产品展示区域 */
.products-section {
    padding: 80px 40px;
    background: var(--light-blue);
}

.products-container {
    max-width: 1920px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 40px;
}

.category-panel {
    position: relative;
    background: linear-gradient(270deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    border-radius: 10px;
    height: fit-content;
    overflow: hidden;
}

.category-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.category-bg img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-content {
    position: relative;
    z-index: 2;
    padding: 40px 30px;
}

.category-panel h3 {
    font-size: 24px;
    margin-bottom: 20px;
}

.view-more {
    color: var(--white);
    text-decoration: none;
    font-size: 14px;
    margin-bottom: 30px;
    display: block;
}

.category-tags {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.tag-row {
    display: flex;
    gap: 10px;
}

.tag {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    flex: 1;
    text-align: center;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.product-card {
    background: var(--white);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    box-shadow: var(--shadow);
}

.product-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 10px;
}

.product-card p {
    font-size: 14px;
    color: var(--text-gray);
}

.view-more-link {
    color: var(--text-gray);
    text-decoration: none;
    text-align: center;
    display: block;
}

/* 标签筛选区域 */
.tags-section {
    padding: 40px;
    background: var(--white);
}

.tags-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    gap: 20px;
}

.tag-item {
    padding: 12px 24px;
    border: 1px solid var(--border-gray);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s;
}

.tag-item.active {
    background: linear-gradient(270deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    border-color: transparent;
}

.tag-item:hover {
    border-color: var(--primary-blue);
}

/* 案例展示区域 */
.case-section {
    padding: 80px 40px;
    background: var(--white);
}

.case-card {
    max-width: 1220px;
    margin: 0 auto;
    background: var(--white);
    border-radius: 16px;
    box-shadow: var(--shadow);
    display: grid;
    grid-template-columns: 1fr 1fr;
    overflow: hidden;
}

.case-image {
    padding: 28px;
}

.case-image img {
    width: 100%;
    height: 364px;
    object-fit: cover;
    border-radius: 8px;
}

.case-content {
    padding: 40px;
}

.case-content h3 {
    font-size: 24px;
    margin-bottom: 30px;
    color: var(--text-dark);
}

.case-divider {
    width: 100px;
    height: 2px;
    background: var(--white);
    margin: 20px 0;
}

.case-divider:first-of-type {
    background: var(--white);
}

.case-divider:last-of-type {
    background: rgba(51, 51, 51, 0.3);
}

.case-detail {
    margin-bottom: 25px;
}

.case-detail h4 {
    font-size: 16px;
    color: var(--text-gray);
    margin-bottom: 10px;
}

.case-detail p {
    font-size: 14px;
    color: var(--text-gray);
    line-height: 1.6;
}

/* 合作伙伴展示区域 */
.partners-section {
    background: linear-gradient(135deg, var(--light-blue) 0%, #CDE1F3 100%);
    padding: 80px 40px;
    position: relative;
}

.partners-bg {
    max-width: 1920px;
    margin: 0 auto;
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-top: 60px;
}

.partner-card {
    background: var(--white);
    border-radius: 8px;
    padding: 24px;
    box-shadow: var(--shadow);
}

.partner-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-blue);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 16px;
}

.partner-card h3 {
    font-size: 20px;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.partner-info {
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 15px;
    color: var(--text-light);
}

.detail-btn {
    background: var(--primary-blue);
    color: var(--white);
    border: none;
    padding: 10px 24px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.detail-btn:hover {
    background: var(--secondary-blue);
}

/* 底部信息区域 */
.footer {
    background: var(--dark-blue);
    color: var(--white);
    padding: 40px;
}

.footer-content {
    max-width: 1920px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-info h3 {
    font-size: 20px;
    margin-bottom: 20px;
}

.contact-links {
    display: flex;
    gap: 40px;
}

.contact-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s;
}

.contact-links a:hover {
    color: var(--white);
}

.footer-qr img {
    width: 72px;
    height: 72px;
}

.footer-bottom {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.65);
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .products-container {
        grid-template-columns: 1fr;
    }
    
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav {
        padding: 0 20px;
        flex-direction: column;
        height: auto;
        padding: 20px;
    }
    
    .nav-left {
        flex-direction: column;
        gap: 20px;
    }
    
    .nav-menu {
        gap: 20px;
    }
    
    .hero-section {
        padding: 40px 20px;
    }
    
    .hero-title {
        font-size: 32px;
    }
    
    .hero-subtitle {
        font-size: 40px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .case-card {
        grid-template-columns: 1fr;
    }
    
    .partners-grid {
        grid-template-columns: 1fr;
    }
    
    .tags-container {
        flex-wrap: wrap;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 20px;
    }
}
